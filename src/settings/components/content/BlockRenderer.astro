---
import editorJ<PERSON>arser from '../../utils/editorjs-parser.js';

export interface Props {
  block: {
    id: string;
    type: string;
    enabled: boolean;
    order: number;
    content: {
      [lang: string]: any;
    };
    name?: string;
  };
  lang?: string;
}

const { block, lang = 'ru' } = Astro.props;

/**
 * Рендерит специальные типы блоков, которые требуют особой обработки
 */
function renderSpecialBlock(block, lang) {
  switch (block.type) {
    case 'slider':
      return renderSliderBlock(block, lang);
    case 'hero':
      return renderHeroBlock(block, lang);
    case 'gallery':
      return renderGalleryBlock(block, lang);
    case 'video':
      return renderVideoBlock(block, lang);
    case 'contacts':
      return renderContactsBlock(block, lang);
    case 'map':
      return renderMapBlock(block, lang);
    default:
      // Для всех остальных типов используем стандартный парсер Editor.js
      return editorJSParser.parseBlockContent(block, lang);
  }
}

/**
 * Рендерит блок слайдера
 */
function renderSliderBlock(block, lang) {
  const content = block.content?.[lang];
  if (!content) return '';
  
  // Если это JSON от Editor.js, парсим его
  if (typeof content === 'string') {
    return editorJSParser.parseToHTML(content);
  }
  
  // Если это старый формат с массивом слайдов
  if (content.slides && Array.isArray(content.slides)) {
    const slides = content.slides.map((slide, index) => 
      `<div class="slide" data-slide="${index}">
        <div class="slide-content p-8 bg-gray-100 rounded-lg">
          <p class="text-lg text-gray-700">${slide}</p>
        </div>
      </div>`
    ).join('');
    
    return `<div class="slider-container">
      <div class="slides">${slides}</div>
      <div class="slider-controls mt-4 text-center">
        <button class="prev-slide bg-blue-600 text-white px-4 py-2 rounded mr-2">Предыдущий</button>
        <button class="next-slide bg-blue-600 text-white px-4 py-2 rounded">Следующий</button>
      </div>
    </div>`;
  }
  
  return '';
}

/**
 * Рендерит hero блок
 */
function renderHeroBlock(block, lang) {
  const content = block.content?.[lang];
  if (!content) return '';
  
  // Если это JSON от Editor.js, парсим его и оборачиваем в hero контейнер
  if (typeof content === 'string') {
    const parsedContent = editorJSParser.parseToHTML(content);
    return `<div class="hero-content bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16 px-8 rounded-lg">
      ${parsedContent}
    </div>`;
  }
  
  return '';
}

/**
 * Рендерит блок галереи
 */
function renderGalleryBlock(block, lang) {
  const content = block.content?.[lang];
  if (!content) return '';
  
  // Если это JSON от Editor.js, парсим его и оборачиваем в галерею
  if (typeof content === 'string') {
    const parsedContent = editorJSParser.parseToHTML(content);
    return `<div class="gallery-content">
      ${parsedContent}
    </div>`;
  }
  
  return '';
}

/**
 * Рендерит блок видео
 */
function renderVideoBlock(block, lang) {
  const content = block.content?.[lang];
  if (!content) return '';
  
  // Если это JSON от Editor.js, парсим его
  if (typeof content === 'string') {
    return editorJSParser.parseToHTML(content);
  }
  
  return '';
}

/**
 * Рендерит блок контактов
 */
function renderContactsBlock(block, lang) {
  const content = block.content?.[lang];
  if (!content) return '';
  
  // Если это JSON от Editor.js, парсим его
  if (typeof content === 'string') {
    return editorJSParser.parseToHTML(content);
  }
  
  return '';
}

/**
 * Рендерит блок карты
 */
function renderMapBlock(block, lang) {
  const content = block.content?.[lang];
  if (!content) return '';
  
  // Если это JSON от Editor.js, парсим его
  if (typeof content === 'string') {
    return editorJSParser.parseToHTML(content);
  }
  
  return '';
}

// Получаем HTML для блока
const blockHTML = renderSpecialBlock(block, lang);
---

{blockHTML && (
  <div 
    class="block-renderer"
    data-block-id={block.id}
    data-block-type={block.type}
    set:html={blockHTML}
  />
)}

<style>
  .block-renderer {
    @apply w-full;
  }
  
  /* Стили для слайдера */
  .slider-container {
    @apply relative;
  }
  
  .slides {
    @apply space-y-4;
  }
  
  .slide {
    @apply w-full;
  }
  
  .slide-content {
    @apply transition-all duration-300;
  }
  
  .slider-controls button {
    @apply transition-colors duration-200 hover:bg-blue-700;
  }
  
  /* Стили для hero блока */
  .hero-content {
    @apply text-center;
  }
  
  .hero-content :global(h1),
  .hero-content :global(h2),
  .hero-content :global(h3) {
    @apply text-white;
  }
  
  .hero-content :global(p) {
    @apply text-blue-100;
  }
  
  /* Стили для галереи */
  .gallery-content {
    @apply grid gap-4;
  }
  
  .gallery-content :global(img) {
    @apply rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200;
  }
  
  /* Адаптивная сетка для галереи */
  @media (min-width: 640px) {
    .gallery-content {
      @apply grid-cols-2;
    }
  }
  
  @media (min-width: 1024px) {
    .gallery-content {
      @apply grid-cols-3;
    }
  }
</style>
