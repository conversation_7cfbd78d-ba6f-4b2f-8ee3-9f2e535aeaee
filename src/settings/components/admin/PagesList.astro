---
import { loadPageSettings } from '../../utils/settingsLoader.js';

const settings = await loadPageSettings();
const pages = settings.pages || [];
---

<div class="max-w-7xl w-full mx-auto bg-white rounded-lg shadow p-8 mt-8">
  <h1 class="text-2xl font-bold text-gray-900 mb-2">Страницы</h1>
  <p class="text-gray-600 mb-6">Управляйте страницами сайта, их структурой, контентом и SEO для каждого языка.</p>
  <div class="flex items-center justify-between mb-4">
    <span class="font-semibold text-lg text-gray-800">Список страниц</span>
    <div class="flex gap-2">
      <a href="/admin/settings/cleanup" class="bg-yellow-600 hover:bg-yellow-700 text-white font-semibold px-4 py-2 rounded shadow transition">Очистка</a>
      <a href="/admin/settings/pages/new" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-4 py-2 rounded shadow transition">Добавить страницу</a>
    </div>
  </div>
  <div class="overflow-x-auto">
    <table class="w-full border rounded-lg overflow-hidden text-sm">
      <thead class="bg-gray-100 text-gray-700">
        <tr>
          <th class="px-4 py-2 text-left">ID</th>
          <th class="px-4 py-2 text-left">URL (RU)</th>
          <th class="px-4 py-2 text-left">URL (EN)</th>
          <th class="px-4 py-2 text-left">Шаблон</th>
          <th class="px-4 py-2 text-left">Видимость</th>
          <th class="px-4 py-2 text-left">Действия</th>
        </tr>
      </thead>
      <tbody>
        {pages.map(page => (
          <tr class="hover:bg-gray-50 border-b last:border-0">
            <td class="px-4 py-2 font-mono text-blue-900">{page.id}</td>
            <td class="px-4 py-2">{page.url?.ru}</td>
            <td class="px-4 py-2">{page.url?.en}</td>
            <td class="px-4 py-2">{page.template}</td>
            <td class="px-4 py-2">{page.visible ? 'Да' : 'Нет'}</td>
            <td class="px-4 py-2 flex gap-2">
              <a href={`/admin/settings/pages/edit/${page.id}`} class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-xs font-semibold shadow transition">Редактировать</a>
              <button type="button" class="bg-red-100 text-red-700 px-3 py-1 rounded text-xs font-semibold cursor-not-allowed" disabled>Удалить</button>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  </div>
</div> 