---
const { blocks = [], pageId } = Astro.props;
---

<div class="space-y-4 mt-4" id="blocks-container">
  {blocks.length === 0 && (
    <div class="text-gray-400 text-sm">Нет блоков на этой странице.</div>
  )}
  {blocks.map((block, idx) => (
    <div class="bg-gray-50 border border-gray-200 rounded-lg shadow-sm p-4 flex flex-col md:flex-row md:items-center md:justify-between gap-4" data-block-id={block.id}>
      <div class="flex-1 min-w-0">
        <div class="flex items-center gap-2 mb-1">
          <span class="font-mono text-blue-900 text-xs">ID: {block.id}</span>
          <span class="inline-block bg-gray-200 text-gray-700 text-xs px-2 py-0.5 rounded">{block.type}</span>
          <span class="inline-block bg-gray-100 text-gray-500 text-xs px-2 py-0.5 rounded">Порядок: {block.order ?? idx + 1}</span>
          {block.enabled ? (
            <span class="inline-block bg-green-100 text-green-700 text-xs px-2 py-0.5 rounded">Вкл.</span>
          ) : (
            <span class="inline-block bg-gray-300 text-gray-500 text-xs px-2 py-0.5 rounded">Выкл.</span>
          )}
        </div>
        <div class="text-sm text-gray-700 truncate">
          <b>{block.name || block.content?.ru?.title || 'Без названия'}</b>
        </div>
      </div>
      <div class="flex gap-2 flex-shrink-0">
        {pageId ? (
          <a href={`/admin/settings/pages/edit/${pageId}/block/edit?blockId=${block.id}`} class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-xs font-semibold shadow transition">Редактировать</a>
        ) : (
          <button type="button" class="btn btn-sm" disabled>Редактировать</button>
        )}
        {pageId ? (
          <button type="button"
                  class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-xs font-semibold shadow transition delete-block-btn"
                  data-page-id={pageId}
                  data-block-id={block.id}
                  title="Удалить блок">
            Удалить
          </button>
        ) : (
          <button type="button" class="bg-red-100 text-red-700 px-3 py-1 rounded text-xs font-semibold cursor-not-allowed" disabled>Удалить</button>
        )}
        {pageId && idx > 0 ? (
          <button type="button"
                  class="bg-gray-500 hover:bg-gray-600 text-white px-2 py-1 rounded text-xs font-semibold shadow transition move-block-btn"
                  data-page-id={pageId}
                  data-block-id={block.id}
                  data-direction="up"
                  title="Переместить вверх">
            ↑
          </button>
        ) : (
          <button type="button" class="bg-gray-200 text-gray-400 px-2 py-1 rounded text-xs font-semibold cursor-not-allowed" disabled title="Нельзя переместить вверх">↑</button>
        )}
        {pageId && idx < blocks.length - 1 ? (
          <button type="button"
                  class="bg-gray-500 hover:bg-gray-600 text-white px-2 py-1 rounded text-xs font-semibold shadow transition move-block-btn"
                  data-page-id={pageId}
                  data-block-id={block.id}
                  data-direction="down"
                  title="Переместить вниз">
            ↓
          </button>
        ) : (
          <button type="button" class="bg-gray-200 text-gray-400 px-2 py-1 rounded text-xs font-semibold cursor-not-allowed" disabled title="Нельзя переместить вниз">↓</button>
        )}
      </div>
    </div>
  ))}
  <div>
    {pageId ? (
      <a href={`/admin/settings/pages/edit/${pageId}/block/new`} class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-4 py-2 rounded shadow transition">Добавить блок</a>
    ) : (
      <button type="button" class="bg-blue-200 text-blue-500 font-semibold px-4 py-2 rounded shadow cursor-not-allowed" disabled>Добавить блок</button>
    )}
  </div>
</div>

<style>
.btn {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 0.25rem 0.75rem;
  margin-right: 0.5rem;
  color: #6b7280;
  cursor: pointer;
  text-decoration: none;
}
.btn-primary {
  background: #2563eb;
  color: #fff;
  border-color: #2563eb;
}
.btn-danger {
  background: #ef4444;
  color: #fff;
  border-color: #ef4444;
}
.btn-sm {
  font-size: 0.9em;
  padding: 0.15rem 0.5rem;
}

/* Стили для кнопок управления блоками */
.delete-block-btn:disabled,
.move-block-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Анимация для перемещения блоков */
.block-moving {
  transition: transform 0.3s ease;
}
</style>

<script is:inline>
document.addEventListener('DOMContentLoaded', function() {
  // Обработчик удаления блока
  document.addEventListener('click', async function(e) {
    if (e.target && e.target.classList && e.target.classList.contains('delete-block-btn')) {
      e.preventDefault();

      const button = e.target;
      const pageId = button.getAttribute('data-page-id');
      const blockId = button.getAttribute('data-block-id');

      if (!confirm('Вы уверены, что хотите удалить этот блок? Это действие нельзя отменить.')) {
        return;
      }

      // Показываем состояние загрузки
      button.disabled = true;
      button.textContent = 'Удаление...';

      try {
        const response = await fetch('/api/settings/blocks', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ pageId, blockId })
        });

        const result = await response.json();

        if (result.success) {
          // Удаляем блок из DOM
          const blockElement = document.querySelector(`[data-block-id="${blockId}"]`);
          if (blockElement) {
            blockElement.style.transition = 'opacity 0.3s ease';
            blockElement.style.opacity = '0';
            setTimeout(() => {
              blockElement.remove();
              // Обновляем страницу для корректного отображения порядка
              window.location.reload();
            }, 300);
          }
        } else {
          alert('Ошибка при удалении блока: ' + result.error);
          button.disabled = false;
          button.textContent = 'Удалить';
        }
      } catch (error) {
        console.error('Ошибка при удалении блока:', error);
        alert('Произошла ошибка при удалении блока');
        button.disabled = false;
        button.textContent = 'Удалить';
      }
    }
  });

  // Обработчик перемещения блока
  document.addEventListener('click', async function(e) {
    if (e.target && e.target.classList && e.target.classList.contains('move-block-btn')) {
      e.preventDefault();

      const button = e.target;
      const pageId = button.getAttribute('data-page-id');
      const blockId = button.getAttribute('data-block-id');
      const direction = button.getAttribute('data-direction');

      // Показываем состояние загрузки
      button.disabled = true;
      const originalText = button.textContent;
      button.textContent = '...';

      try {
        const response = await fetch('/api/settings/blocks', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ pageId, blockId, direction })
        });

        const result = await response.json();

        if (result.success) {
          // Перезагружаем страницу для корректного отображения
          window.location.reload();
        } else {
          alert('Ошибка при перемещении блока: ' + result.error);
          button.disabled = false;
          button.textContent = originalText;
        }
      } catch (error) {
        console.error('Ошибка при перемещении блока:', error);
        alert('Произошла ошибка при перемещении блока');
        button.disabled = false;
        button.textContent = originalText;
      }
    }
  });
});
</script>