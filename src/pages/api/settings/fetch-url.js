// API для получения мета-данных ссылок для EditorJS LinkTool (система настроек)
import fetch from 'node-fetch';
import { isAuthenticated } from '../../../utils/auth';

export async function GET({ request }) {
  try {
    console.log('🔗 Получен запрос на получение информации о ссылке (настройки)');

    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({
        success: 0,
        message: 'Не авторизован'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const url = new URL(request.url);
    const targetUrl = url.searchParams.get('url');

    if (!targetUrl) {
      return new Response(JSON.stringify({
        success: 0,
        message: 'URL не указан'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Проверяем валидность URL
    let validUrl;
    try {
      validUrl = new URL(targetUrl);
    } catch (error) {
      return new Response(JSON.stringify({
        success: 0,
        message: 'Некорректный URL'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Проверяем протокол
    if (!['http:', 'https:'].includes(validUrl.protocol)) {
      return new Response(JSON.stringify({
        success: 0,
        message: 'Поддерживаются только HTTP и HTTPS ссылки'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    try {
      // Получаем HTML страницы
      const response = await fetch(targetUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; EditorJS LinkTool)'
        },
        timeout: 10000
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const html = await response.text();

      // Извлекаем мета-данные
      const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
      const descriptionMatch = html.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i) ||
                              html.match(/<meta[^>]*content=["\']([^"']+)["\'][^>]*name=["\']description["\'][^>]*>/i);
      const imageMatch = html.match(/<meta[^>]*property=["\']og:image["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i) ||
                        html.match(/<meta[^>]*content=["\']([^"']+)["\'][^>]*property=["\']og:image["\'][^>]*>/i);

      const title = titleMatch ? titleMatch[1].trim() : validUrl.hostname;
      const description = descriptionMatch ? descriptionMatch[1].trim() : '';
      const image = imageMatch ? imageMatch[1].trim() : '';

      console.log(`✅ Получена информация о ссылке: ${title}`);

      return new Response(JSON.stringify({
        success: 1,
        link: targetUrl,
        meta: {
          title: title,
          description: description,
          image: {
            url: image
          }
        }
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });

    } catch (fetchError) {
      console.error('❌ Ошибка при получении страницы:', fetchError);

      // Возвращаем базовую информацию
      return new Response(JSON.stringify({
        success: 1,
        link: targetUrl,
        meta: {
          title: validUrl.hostname,
          description: 'Не удалось получить описание',
          image: {
            url: ''
          }
        }
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('❌ Ошибка при обработке ссылки:', error);
    
    return new Response(JSON.stringify({
      success: 0,
      message: 'Ошибка сервера при обработке ссылки'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function POST({ request }) {
  return GET({ request });
}
