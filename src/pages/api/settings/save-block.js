// API для сохранения блоков страниц (система настроек)
import { loadPageSettings } from '../../../settings/utils/settingsLoader.js';
import { savePageSettings } from '../../../settings/utils/settingsSaver.js';
import { isAuthenticated } from '../../../utils/auth';

export async function POST({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response('Не авторизован', { status: 401 });
    }

    const formData = await request.formData();
    const data = Object.fromEntries(formData.entries());

    const pageId = data.id;
    const blockId = data.blockId;
    const blockName = data.blockName;
    const lang = data.lang || 'ru';
    const content = data.content || '';

    const settings = await loadPageSettings();
    let pages = settings.pages || [];
    const pageIdx = pages.findIndex(p => p.id === pageId);

    if (pageIdx === -1) {
      return new Response('Страница не найдена', { status: 404 });
    }

    const page = pages[pageIdx];
    let blocks = page.blocks || [];
    let block = blocks.find(b => b.id === blockId);

    // Вычисляем правильный порядок
    let order = Number(data.order);
    if (!order || order <= 0) {
      // Автоматическое назначение порядка
      const maxOrder = blocks.length > 0 ? Math.max(...blocks.map(b => b.order || 0)) : 0;
      order = maxOrder + 1;
    }

    if (!block) {
      // Новый блок
      block = {
        id: blockId,
        name: blockName || '',
        type: data.type || 'text',
        order: order,
        enabled: data.enabled === 'on',
        content: { ru: '', en: '' }
      };
      blocks.push(block);
    } else {
      // Обновление существующего блока
      if (blockName !== undefined) block.name = blockName;
      if (data.type) block.type = data.type;
      if (data.order) block.order = Number(data.order);
      if (data.enabled !== undefined) block.enabled = data.enabled === 'on';
    }

    // Обновить контент для языка
    block.content = block.content || { ru: '', en: '' };
    block.content[lang] = content;

    // Обновить массив блоков и страницу
    page.blocks = blocks;
    pages[pageIdx] = page;
    settings.pages = pages;

    await savePageSettings(settings);

    return new Response(null, {
      status: 303,
      headers: { Location: `/admin/settings/pages/edit/${pageId}` }
    });

  } catch (error) {
    return new Response('Ошибка сервера при сохранении блока', { status: 500 });
  }
}
