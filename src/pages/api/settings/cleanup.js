import { cleanupDuplicatePages, checkForDuplicates } from '../../../settings/utils/cleanupPages.js';
import { isAuthenticated } from '../../../utils/auth.js';

// GET - проверка наличия дублирующих страниц
export async function GET({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const result = await checkForDuplicates();
    
    return new Response(JSON.stringify({
      success: true,
      ...result
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Ошибка при проверке дубликатов:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Внутренняя ошибка сервера' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// POST - очистка дублирующих страниц
export async function POST({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const result = await cleanupDuplicatePages();
    
    if (result.success) {
      return new Response(JSON.stringify({
        success: true,
        message: `Очистка завершена. Удалено ${result.removedCount} дублирующих записей.`,
        ...result
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    } else {
      return new Response(JSON.stringify({
        success: false,
        error: result.error
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('Ошибка при очистке дубликатов:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Внутренняя ошибка сервера' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
