import { loadPageSettings } from '../../../settings/utils/settingsLoader.js';
import { savePageSettings } from '../../../settings/utils/settingsSaver.js';
import { isAuthenticated } from '../../../utils/auth.js';

// DELETE - удаление блока
export async function DELETE({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { pageId, blockId } = body;

    if (!pageId || !blockId) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Необходимо указать pageId и blockId' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const settings = await loadPageSettings();
    let pages = settings.pages || [];
    const pageIdx = pages.findIndex(p => p.id === pageId);
    
    if (pageIdx === -1) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Страница не найдена' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const page = pages[pageIdx];
    let blocks = page.blocks || [];
    
    // Находим и удаляем блок
    const blockIndex = blocks.findIndex(block => block.id === blockId);
    if (blockIndex === -1) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Блок не найден' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    blocks.splice(blockIndex, 1);
    
    // Обновляем порядок блоков
    blocks.forEach((block, index) => {
      block.order = index + 1;
    });

    page.blocks = blocks;
    pages[pageIdx] = page;
    settings.pages = pages;
    await savePageSettings(settings);

    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Блок удален' 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Ошибка при удалении блока:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Внутренняя ошибка сервера' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// PUT - перемещение блока
export async function PUT({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { pageId, blockId, direction } = body;

    if (!pageId || !blockId || !direction) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Необходимо указать pageId, blockId и direction (up/down)' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (direction !== 'up' && direction !== 'down') {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'direction должен быть "up" или "down"' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const settings = await loadPageSettings();
    let pages = settings.pages || [];
    const pageIdx = pages.findIndex(p => p.id === pageId);
    
    if (pageIdx === -1) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Страница не найдена' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const page = pages[pageIdx];
    let blocks = page.blocks || [];
    
    // Находим блок
    const blockIndex = blocks.findIndex(block => block.id === blockId);
    if (blockIndex === -1) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Блок не найден' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Проверяем возможность перемещения
    if (direction === 'up' && blockIndex === 0) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Блок уже находится в начале списка' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (direction === 'down' && blockIndex === blocks.length - 1) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Блок уже находится в конце списка' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Перемещаем блок
    const targetIndex = direction === 'up' ? blockIndex - 1 : blockIndex + 1;
    const [movedBlock] = blocks.splice(blockIndex, 1);
    blocks.splice(targetIndex, 0, movedBlock);
    
    // Обновляем порядок блоков
    blocks.forEach((block, index) => {
      block.order = index + 1;
    });

    page.blocks = blocks;
    pages[pageIdx] = page;
    settings.pages = pages;
    await savePageSettings(settings);

    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Блок перемещен' 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Ошибка при перемещении блока:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Внутренняя ошибка сервера' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
