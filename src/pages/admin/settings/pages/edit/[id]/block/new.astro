---
import AdminLayout from '@layouts/AdminLayout.astro';
import AdvancedEditorJS from '../../../../../../../components/editor/AdvancedEditorJS.astro';

const { id } = Astro.params;
---

<AdminLayout title={`Добавить блок на страницу: ${id}`}>
  <div class="max-w-7xl w-full mx-auto bg-white rounded-lg shadow p-8 mt-8">
    <h1 class="text-2xl font-bold text-gray-900 mb-2">Добавить блок</h1>
    <p class="text-gray-600 mb-6">Заполните параметры блока и добавьте контент. После сохранения блок появится в списке на странице.</p>
    <form action="/api/settings/save-block" method="POST" class="space-y-6" onsubmit="return handleFormSubmit()">
      <input type="hidden" name="id" value={id} />
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">Тип блока:</label>
          <select name="type" id="block-type" class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" onchange="updateEditorTools()">
            <option value="text">Текстовый</option>
            <option value="hero">Герой</option>
            <option value="features">Особенности</option>
            <option value="gallery">Галерея</option>
            <option value="contacts">Контакты</option>
            <option value="map">Карта</option>
            <option value="video">Видео</option>
            <option value="forms">Формы</option>
            <option value="custom">Custom (все инструменты)</option>
          </select>
        </div>
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">Название блока:</label>
          <input name="blockName" placeholder="Введите название блока..." required class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
        </div>
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">ID блока:</label>
          <input name="blockId" value={Math.random().toString(36).slice(2,10)} required class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
        </div>
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">Порядок:</label>
          <input name="order" type="number" value="" min="1" placeholder="Автоматически" class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
          <small class="text-gray-500">Оставьте пустым для автоматического назначения</small>
        </div>
        <div class="flex items-center mt-6">
          <input type="checkbox" name="enabled" checked class="mr-2 rounded border-gray-300 focus:ring-blue-500" />
          <label class="text-sm text-gray-700">Включён</label>
        </div>
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">Язык:</label>
          <select name="lang" class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500">
            <option value="ru">Русский</option>
            <option value="en">English</option>
          </select>
        </div>
      </div>
      <div>
        <label class="block mb-1 text-sm font-medium text-gray-700">Контент (Editor.js):</label>
        <div id="editor-container">
          <AdvancedEditorJS id="content" placeholder="Начните создавать контент блока..." minHeight={400} blockType="text" />
        </div>
      </div>
      <div class="flex gap-4 mt-8">
        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded shadow transition">Сохранить</button>
        <a href={`/admin/settings/pages/edit/${id}`} class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded">Отмена</a>
      </div>
    </form>
  </div>
  <script is:inline>
    // Функция для обновления инструментов редактора при изменении типа блока
    function updateEditorTools() {
      const blockTypeSelect = document.getElementById('block-type');
      if (blockTypeSelect) {
        const newBlockType = blockTypeSelect.value;
        console.log(`🔄 Выбран тип блока: ${newBlockType}`);

        // Информация о доступных инструментах для каждого типа
        const toolsInfo = {
          text: ['Заголовки', 'Параграфы', 'Списки', 'Цитаты', 'Код', 'Разделители', 'Таблицы', 'Ссылки', 'HTML'],
          hero: ['Большие заголовки', 'Параграфы', 'Изображения', 'Ссылки', 'HTML'],
          features: ['Заголовки', 'Параграфы', 'Списки', 'Изображения', 'HTML'],
          gallery: ['Заголовки', 'Параграфы', 'Изображения', 'Разделители', 'HTML'],
          contacts: ['Заголовки', 'Параграфы', 'Списки', 'Ссылки', 'HTML'],
          map: ['Заголовки', 'Параграфы', 'Встраивание карт', 'Кнопки', 'HTML'],
          video: ['Заголовки', 'Параграфы', 'Встраивание видео', 'Кнопки', 'HTML'],
          forms: ['Заголовки', 'Параграфы', 'Списки', 'Кнопки', 'HTML'],
          custom: ['Заголовки', 'Параграфы', 'Списки', 'Цитаты', 'Код', 'Таблицы', 'Изображения', 'Ссылки', 'Встраивание', 'Кнопки', 'HTML', 'Разделители']
        };

        const tools = toolsInfo[newBlockType] || toolsInfo.text;
        console.log(`🛠️ Доступные инструменты: ${tools.join(', ')}`);

        // Показываем уведомление пользователю
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded shadow-lg z-50';
        notification.textContent = `Выбран тип "${newBlockType}". Доступные инструменты: ${tools.join(', ')}`;
        document.body.appendChild(notification);

        setTimeout(() => {
          notification.remove();
        }, 5000);
      }
    }

    // Обработчик отправки формы
    async function handleFormSubmit() {
      try {
        console.log('📝 Начинаем сохранение данных из EditorJS...');

        // Получаем экземпляр редактора
        const editorInstance = window['editor-content_instance'];
        const contentInput = document.getElementById('content-data'); // Используем правильный ID

        console.log('🔍 Проверяем экземпляр редактора:', editorInstance);
        console.log('🔍 Проверяем поле ввода:', contentInput);

        if (editorInstance && contentInput) {
          console.log('📝 Сохраняем данные из EditorJS...');

          // Получаем данные из редактора
          const outputData = await editorInstance.save();
          contentInput.value = JSON.stringify(outputData);

          console.log('✅ Данные сохранены:', outputData);
          console.log('✅ Значение поля:', contentInput.value);
          return true;
        } else {
          console.error('❌ Редактор или поле не найдены');
          console.log('🔍 Доступные экземпляры редакторов:', Object.keys(window).filter(key => key.includes('editor')));

          // Попробуем использовать глобальную переменную как fallback
          if (contentInput && window.editorjsLastData) {
            console.log('🔄 Используем fallback данные:', window.editorjsLastData);
            contentInput.value = window.editorjsLastData;
            return true;
          }

          alert('Ошибка: не удалось получить данные из редактора');
          return false;
        }
      } catch (error) {
        console.error('❌ Ошибка при сохранении:', error);
        alert('Ошибка при сохранении данных');
        return false;
      }
    }
  </script>
</AdminLayout>