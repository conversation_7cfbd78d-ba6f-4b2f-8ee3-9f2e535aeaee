---
import BlockRenderer from './BlockRenderer.astro';

export interface Props {
  blocks: Array<{
    id: string;
    type: string;
    enabled: boolean;
    order: number;
    content: {
      [lang: string]: any;
    };
    name?: string;
  }>;
  lang?: string;
  containerClass?: string;
}

const { blocks = [], lang = 'ru', containerClass = '' } = Astro.props;

// Фильтруем и сортируем блоки
const enabledBlocks = blocks
  .filter(block => block.enabled)
  .sort((a, b) => (a.order || 0) - (b.order || 0));

/**
 * Получает CSS классы для контейнера блока в зависимости от типа
 */
function getBlockContainerClass(blockType) {
  const baseClasses = 'block-content mb-8';
  
  switch (blockType) {
    case 'hero':
      return `${baseClasses} hero-block`;
    case 'text':
      return `${baseClasses} text-block prose prose-lg max-w-none`;
    case 'gallery':
      return `${baseClasses} gallery-block`;
    case 'video':
      return `${baseClasses} video-block`;
    case 'contacts':
      return `${baseClasses} contacts-block`;
    case 'map':
      return `${baseClasses} map-block`;
    case 'slider':
      return `${baseClasses} slider-block`;
    case 'custom':
      return `${baseClasses} custom-block prose prose-lg max-w-none`;
    default:
      return `${baseClasses} default-block prose prose-lg max-w-none`;
  }
}

/**
 * Проверяет, есть ли контент в блоке
 */
function hasBlockContent(block, lang) {
  try {
    const content = block?.content?.[lang];
    if (!content) return false;

    // Если это JSON строка, проверяем что она не пустая
    if (typeof content === 'string') {
      if (content.trim() === '') return false;
      try {
        const parsed = JSON.parse(content);
        return parsed.blocks && parsed.blocks.length > 0;
      } catch {
        return content.trim() !== '';
      }
    }

    // Если это объект, проверяем что есть данные
    if (typeof content === 'object') {
      return Object.keys(content).length > 0;
    }

    return false;
  } catch (error) {
    console.error(`Ошибка проверки контента блока ${block.id}:`, error);
    return false;
  }
}
---

<div class={`content-renderer ${containerClass}`}>
  {enabledBlocks.map(block => {
    // Пропускаем блоки без контента
    if (!hasBlockContent(block, lang)) {
      return null;
    }

    return (
      <section
        class={getBlockContainerClass(block.type)}
        data-block-id={block.id}
        data-block-type={block.type}
        data-block-order={block.order}
      >
        {/* Добавляем заголовок блока если есть name (только в режиме разработки) */}
        {import.meta.env.DEV && block.name && (
          <div class="block-debug-info bg-yellow-100 border border-yellow-300 p-2 mb-4 rounded text-xs text-yellow-800">
            <strong>Блок:</strong> {block.name}
            <span class="ml-2 text-yellow-600">({block.type} #{block.order})</span>
          </div>
        )}

        {/* Рендерим блок через BlockRenderer */}
        <BlockRenderer block={block} lang={lang} />
      </section>
    );
  })}
  
  {/* Показываем сообщение если нет активных блоков */}
  {enabledBlocks.length === 0 && (
    <div class="no-content-message text-center py-12">
      <p class="text-gray-500 text-lg">Контент для отображения отсутствует</p>
      {import.meta.env.DEV && (
        <p class="text-gray-400 text-sm mt-2">
          Добавьте блоки контента в админ-панели или проверьте настройки языка
        </p>
      )}
    </div>
  )}
</div>

<style>
  /* Стили для блоков контента */
  .content-renderer {
    @apply w-full;
  }
  
  .block-content {
    @apply relative;
  }
  
  /* Специальные стили для разных типов блоков */
  .hero-block {
    @apply mb-0; /* Hero блоки обычно без отступа снизу */
  }
  
  .text-block {
    @apply max-w-4xl mx-auto; /* Ограничиваем ширину текстовых блоков */
  }
  
  .gallery-block {
    @apply w-full; /* Галереи на всю ширину */
  }
  
  .video-block {
    @apply max-w-4xl mx-auto; /* Видео с ограниченной шириной */
  }
  
  .contacts-block {
    @apply w-full; /* Контакты на всю ширину */
  }
  
  .map-block {
    @apply w-full; /* Карты на всю ширину */
  }
  
  .slider-block {
    @apply w-full mb-0; /* Слайдеры на всю ширину без отступа */
  }
  
  .custom-block {
    @apply max-w-4xl mx-auto; /* Кастомные блоки с ограниченной шириной */
  }
  
  /* Отладочная информация только в режиме разработки */
  .block-debug-info {
    font-family: 'Courier New', monospace;
  }
  
  /* Стили для контента из Editor.js */
  .block-content :global(h1),
  .block-content :global(h2),
  .block-content :global(h3),
  .block-content :global(h4),
  .block-content :global(h5),
  .block-content :global(h6) {
    @apply font-bold text-gray-900 leading-tight;
  }
  
  .block-content :global(p) {
    @apply text-gray-700 leading-relaxed;
  }
  
  .block-content :global(a) {
    @apply text-blue-600 hover:text-blue-800 underline;
  }
  
  .block-content :global(img) {
    @apply max-w-full h-auto;
  }
  
  .block-content :global(blockquote) {
    @apply border-l-4 border-blue-500 pl-6 italic;
  }
  
  .block-content :global(code) {
    @apply bg-gray-100 px-2 py-1 rounded text-sm font-mono;
  }
  
  .block-content :global(pre) {
    @apply bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto;
  }
  
  .block-content :global(pre code) {
    @apply bg-transparent p-0;
  }
  
  .block-content :global(ul),
  .block-content :global(ol) {
    @apply space-y-2;
  }
  
  .block-content :global(li) {
    @apply text-gray-700;
  }
  
  .block-content :global(table) {
    @apply w-full border-collapse;
  }
  
  .block-content :global(th),
  .block-content :global(td) {
    @apply border border-gray-300 px-4 py-2;
  }
  
  .block-content :global(th) {
    @apply bg-gray-100 font-semibold;
  }
  
  /* Адаптивность для мобильных устройств */
  @media (max-width: 768px) {
    .text-block,
    .video-block,
    .custom-block {
      @apply max-w-none mx-4;
    }
    
    .block-content :global(iframe) {
      @apply w-full h-auto;
    }
    
    .block-content :global(table) {
      @apply text-sm;
    }
    
    .block-content :global(th),
    .block-content :global(td) {
      @apply px-2 py-1;
    }
  }
</style>
