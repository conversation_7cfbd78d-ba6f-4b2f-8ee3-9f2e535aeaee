import edjsHTML from 'editorjs-html';

/**
 * Парсер для преобразования Editor.js JSON в HTML
 */
class EditorJSParser {
  constructor() {
    // Создаем экземпляр парсера с базовыми настройками
    this.parser = edjsHTML();
  }

  /**
   * Применяет стили Tailwind CSS к HTML
   */
  applyTailwindStyles(html) {
    return html
      // Заголовки
      .replace(/<h1>/g, '<h1 class="text-4xl font-bold mb-4 text-gray-900">')
      .replace(/<h2>/g, '<h2 class="text-3xl font-bold mb-4 text-gray-900">')
      .replace(/<h3>/g, '<h3 class="text-2xl font-bold mb-4 text-gray-900">')
      .replace(/<h4>/g, '<h4 class="text-xl font-bold mb-4 text-gray-900">')
      .replace(/<h5>/g, '<h5 class="text-lg font-bold mb-4 text-gray-900">')
      .replace(/<h6>/g, '<h6 class="text-base font-bold mb-4 text-gray-900">')

      // Параграфы
      .replace(/<p>/g, '<p class="mb-4 text-gray-700 leading-relaxed">')

      // Списки
      .replace(/<ul>/g, '<ul class="list-disc list-inside mb-6 space-y-2">')
      .replace(/<ol>/g, '<ol class="list-decimal list-inside mb-6 space-y-2">')
      .replace(/<li>/g, '<li class="mb-2 text-gray-700">')

      // Изображения
      .replace(/<img([^>]*)>/g, '<img$1 class="max-w-full h-auto rounded-lg shadow-md mb-6">')

      // Цитаты
      .replace(/<blockquote>/g, '<blockquote class="border-l-4 border-blue-500 pl-6 py-4 mb-6 bg-gray-50 italic">')

      // Код
      .replace(/<pre>/g, '<pre class="bg-gray-900 text-green-400 p-4 rounded-lg mb-6 overflow-x-auto">')
      .replace(/<code>/g, '<code class="text-sm font-mono">')

      // Ссылки
      .replace(/<a([^>]*)>/g, '<a$1 class="text-blue-600 hover:text-blue-800 underline">')

      // Таблицы
      .replace(/<table>/g, '<table class="min-w-full border border-gray-300 rounded-lg mb-6">')
      .replace(/<th>/g, '<th class="px-4 py-2 bg-gray-100 font-semibold text-left border-b">')
      .replace(/<td>/g, '<td class="px-4 py-2 border-b">')

      // Разделители
      .replace(/<hr>/g, '<hr class="my-8 border-t border-gray-300">');
  }

  /**
   * Получает размер заголовка для Tailwind CSS
   */
  getHeaderSize(level) {
    return this.getHeaderSizeStatic(level);
  }

  /**
   * Статический метод для получения размера заголовка
   */
  getHeaderSizeStatic(level) {
    const sizes = {
      1: '4xl',
      2: '3xl',
      3: '2xl',
      4: 'xl',
      5: 'lg',
      6: 'base'
    };
    return sizes[level] || 'xl';
  }

  /**
   * Экранирует HTML символы
   */
  escapeHtml(text) {
    return this.escapeHtmlStatic(text);
  }

  /**
   * Статический метод для экранирования HTML символов
   */
  escapeHtmlStatic(text) {
    if (typeof text !== 'string') return '';

    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  /**
   * Парсит JSON строку от Editor.js в HTML
   * @param {string} jsonString - JSON строка от Editor.js
   * @returns {string} HTML строка
   */
  parseToHTML(jsonString) {
    try {
      // Проверяем, является ли входная строка JSON
      if (!jsonString || typeof jsonString !== 'string') {
        return '';
      }

      // Пытаемся распарсить JSON
      const editorData = JSON.parse(jsonString);
      
      // Проверяем структуру данных Editor.js
      if (!editorData.blocks || !Array.isArray(editorData.blocks)) {
        return '';
      }

      // Преобразуем блоки в HTML
      const htmlResult = this.parser.parse(editorData);

      // Проверяем тип результата
      let htmlString = '';
      if (typeof htmlResult === 'string') {
        htmlString = htmlResult;
      } else if (Array.isArray(htmlResult)) {
        htmlString = htmlResult.join('');
      } else {
        console.error('Парсер вернул неожиданный тип:', typeof htmlResult, htmlResult);
        return '';
      }

      // Применяем стили Tailwind CSS
      return this.applyTailwindStyles(htmlString);
      
    } catch (error) {
      console.error('Ошибка парсинга Editor.js JSON:', error);
      // Возвращаем исходную строку как fallback
      return jsonString;
    }
  }

  /**
   * Парсит контент блока с учетом языка
   * @param {Object} block - Блок контента
   * @param {string} lang - Язык (ru, en)
   * @returns {string} HTML строка
   */
  parseBlockContent(block, lang = 'ru') {
    if (!block || !block.content || !block.content[lang]) {
      return '';
    }

    const content = block.content[lang];
    
    // Если контент - это строка JSON от Editor.js
    if (typeof content === 'string') {
      return this.parseToHTML(content);
    }
    
    // Если контент - это объект с title и body (старый формат)
    if (typeof content === 'object' && content.title && content.body) {
      return `
        <h2 class="text-3xl font-bold mb-4 text-gray-900">${content.title}</h2>
        <div class="text-gray-700">${content.body}</div>
      `;
    }
    
    return '';
  }
}

// Создаем глобальный экземпляр парсера
const editorJSParser = new EditorJSParser();

export default editorJSParser;
