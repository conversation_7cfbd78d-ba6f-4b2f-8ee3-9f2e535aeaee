# Система рендеринга контента

Система рендеринга контента позволяет преобразовывать JSON данные от Editor.js в HTML для отображения на сайте.

> **Примечание**: Все компоненты системы рендеринга контента находятся в папке `src/settings/` для удобства интеграции в другие проекты.

## Компоненты

### 1. EditorJSParser (`src/settings/utils/editorjs-parser.js`)

Основной парсер для преобразования Editor.js JSON в HTML.

**Поддерживаемые блоки:**
- `header` - Заголовки (H1-H6)
- `paragraph` - Параграфы текста
- `list` - Списки (маркированные, нумерованные, чек-листы)
- `image` - Изображения с подписями
- `quote` - Цитаты
- `code` - Блоки кода
- `delimiter` - Разделители
- `table` - Таблицы
- `linkTool` - Ссылки с превью
- `embed` - Встраиваемый контент (YouTube и др.)
- `button` - Кнопки
- `raw` - Сырой HTML

**Основные методы:**
```javascript
// Парсинг JSON строки в HTML
const html = editorJSParser.parseToHTML(jsonString);

// Парсинг контента блока с учетом языка
const html = editorJSParser.parseBlockContent(block, 'ru');
```

### 2. ContentRenderer (`src/settings/components/content/ContentRenderer.astro`)

Компонент для рендеринга массива блоков контента.

**Использование:**
```astro
<ContentRenderer
  blocks={page.blocks || []}
  lang="ru"
  containerClass="page-content"
/>
```

**Параметры:**
- `blocks` - Массив блоков контента
- `lang` - Язык контента (по умолчанию 'ru')
- `containerClass` - CSS класс для контейнера

### 3. BlockRenderer (`src/settings/components/content/BlockRenderer.astro`)

Компонент для рендеринга отдельного блока с поддержкой специальных типов.

**Специальные типы блоков:**
- `slider` - Слайдеры
- `hero` - Hero блоки
- `gallery` - Галереи
- `video` - Видео блоки
- `contacts` - Контактные блоки
- `map` - Карты

## Стили

Все блоки используют Tailwind CSS классы для стилизации:

- Заголовки: `text-{size} font-bold mb-4 text-gray-900`
- Параграфы: `mb-4 text-gray-700 leading-relaxed`
- Списки: `list-{type} list-inside mb-6 space-y-2`
- Изображения: `rounded-lg shadow-md`
- Цитаты: `border-l-4 border-blue-500 pl-6 py-4 mb-6 bg-gray-50 italic`
- Код: `bg-gray-900 text-green-400 p-4 rounded-lg mb-6 overflow-x-auto`

## Типы блоков и их CSS классы

```css
.hero-block { @apply mb-0; }
.text-block { @apply max-w-4xl mx-auto; }
.gallery-block { @apply w-full; }
.video-block { @apply max-w-4xl mx-auto; }
.contacts-block { @apply w-full; }
.map-block { @apply w-full; }
.slider-block { @apply w-full mb-0; }
.custom-block { @apply max-w-4xl mx-auto; }
```

## Отладка

В режиме разработки (`import.meta.env.DEV`) отображается дополнительная информация о блоках:

```html
<div class="block-debug-info">
  <strong>Блок:</strong> Название блока
  <span>(тип #порядок)</span>
</div>
```

## Обработка ошибок

Парсер включает обработку ошибок для:
- Неполных данных блоков
- Отсутствующих файлов изображений
- Некорректного JSON
- Пустых блоков

При ошибках отображаются информативные сообщения вместо поломки страницы.

## Адаптивность

Все блоки адаптированы для мобильных устройств:
- Изображения масштабируются
- Таблицы становятся прокручиваемыми
- Текстовые блоки получают отступы на мобильных

## Расширение

Для добавления новых типов блоков:

1. Добавьте парсер в `EditorJSParser`
2. Добавьте обработку в `BlockRenderer` (если нужна специальная логика)
3. Добавьте CSS стили в `ContentRenderer`

Пример добавления нового блока:

```javascript
// В EditorJSParser
myCustomBlock: (data) => {
  if (!data || !data.content) {
    return '<p class="text-gray-500 mb-4">Контент не найден</p>';
  }
  return `<div class="my-custom-block">${data.content}</div>`;
}
```

## Использование в страницах

```astro
---
import { loadPageSettings } from '../settings/utils/settingsLoader.js';
import ContentRenderer from '../settings/components/content/ContentRenderer.astro';

const settings = await loadPageSettings();
const page = settings.pages.find(p => p.id === 'my-page');
---

<div class="container mx-auto px-4 py-8">
  <ContentRenderer 
    blocks={page.blocks || []} 
    lang="ru"
  />
</div>
```
