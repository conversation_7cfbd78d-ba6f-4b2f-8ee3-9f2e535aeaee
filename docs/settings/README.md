# Система настроек страниц

Комплексная система управления страницами сайта с поддержкой блочного редактирования контента на основе EditorJS.

## 📋 Содержание

- [Обзор системы](#обзор-системы)
- [Архитектура](#архитектура)
- [Типы блоков](#типы-блоков)
- [API](#api)
- [Использование](#использование)
- [Конфигурация](#конфигурация)

## 🎯 Обзор системы

Система настроек страниц предоставляет:

- **Управление страницами** - создание, редактирование, удаление страниц
- **Блочный редактор** - гибкая система блоков с различными типами контента
- **Многоязычность** - поддержка русского и английского языков
- **SEO-оптимизация** - настройка мета-тегов для каждой страницы
- **Медиа-управление** - загрузка и управление изображениями
- **Типизированные блоки** - специализированные редакторы для разных типов контента

## 🏗️ Архитектура

```
src/
├── settings/
│   ├── components/admin/          # Компоненты админки
│   │   ├── PageEditForm.astro     # Форма редактирования страницы
│   │   └── blocks/
│   │       └── BlockEditor.astro  # Редактор блоков
│   ├── data/
│   │   └── page.json             # Данные страниц
│   ├── utils/
│   │   ├── settingsLoader.js     # Загрузка настроек
│   │   └── settingsSaver.js      # Сохранение настроек
│   └── types.ts                  # TypeScript типы
│   ├── components/
│   │   ├── content/              # Компоненты рендеринга контента
│   │   │   ├── ContentRenderer.astro
│   │   │   └── BlockRenderer.astro
│   │   └── editor/               # Компоненты редактора
│   │       ├── AdvancedEditorJS.astro
│   │       └── EditorJS.astro
└── pages/
    ├── api/settings/             # API endpoints
    │   ├── pages.js              # API страниц
    │   ├── blocks.js             # API блоков
    │   ├── save-page.js          # Сохранение страниц
    │   └── save-block.js         # Сохранение блоков
    └── admin/settings/pages/     # Админка страниц
        ├── index.astro           # Список страниц
        ├── new.astro             # Создание страницы
        └── edit/[id]/            # Редактирование
            ├── [id].astro        # Редактирование страницы
            └── block/
                ├── new.astro     # Создание блока
                └── edit.astro    # Редактирование блока
```

## 📦 Типы блоков

Система поддерживает следующие типы блоков:

### 1. **Text** - Текстовый блок
- Заголовки (H1-H6)
- Параграфы с inline-форматированием
- Списки (упорядоченные, неупорядоченные, чек-листы)
- Цитаты с автором
- Блоки кода
- Таблицы
- Ссылки с превью
- HTML код
- Разделители

### 2. **Hero** - Героический блок
- Большие заголовки (H1-H2)
- Параграфы
- Изображения с загрузкой
- Ссылки
- HTML код

### 3. **Features** - Блок особенностей
- Заголовки (H2-H4)
- Параграфы
- Списки
- Изображения
- HTML код

### 4. **Gallery** - Галерея
- Заголовки
- Параграфы
- Изображения
- Разделители
- HTML код

### 5. **Contacts** - Контакты
- Заголовки
- Параграфы
- Списки
- Ссылки
- HTML код (для форм)

### 6. **Map** - Карты
- Заголовки
- Параграфы
- Встраивание карт (Google Maps, Яндекс.Карты)
- Кнопки
- HTML код

### 7. **Video** - Видео
- Заголовки
- Параграфы
- Встраивание видео (YouTube, Vimeo, социальные сети)
- Кнопки
- HTML код

### 8. **Forms** - Формы
- Заголовки
- Параграфы
- Списки
- Кнопки
- HTML код (для полей форм)

### 9. **Custom** - Универсальный блок
- **ВСЕ инструменты** из всех типов блоков
- Максимальная гибкость
- Подходит для сложного контента

## 🔧 Конфигурация блоков

Каждый блок имеет следующие свойства:

```typescript
interface BlockConfig {
  id: string;           // Уникальный идентификатор
  name?: string;        // Название блока (отображается в админке)
  type: string;         // Тип блока (text, hero, features, etc.)
  enabled: boolean;     // Включен ли блок
  order?: number;       // Порядок отображения (автоматический если не указан)
  content: {            // Контент для разных языков
    ru: string;         // JSON данные EditorJS для русского языка
    en: string;         // JSON данные EditorJS для английского языка
  };
}
```

## 🛠️ Использование

### Создание новой страницы

1. Перейдите в админку: `/admin/settings/pages`
2. Нажмите "Создать страницу"
3. Заполните основные данные:
   - ID страницы
   - URL для разных языков
   - Шаблон
   - SEO-данные
4. Сохраните страницу

### Добавление блоков

1. Откройте страницу для редактирования
2. В разделе "Блоки страницы" нажмите "Добавить блок"
3. Выберите тип блока
4. Введите название блока
5. Настройте порядок (или оставьте автоматический)
6. Создайте контент в редакторе
7. Сохраните блок

### Редактирование блоков

1. В списке блоков нажмите "Редактировать"
2. Измените настройки или контент
3. Сохраните изменения

### Управление порядком блоков

- Используйте кнопки ↑ и ↓ для перемещения блоков
- Или измените поле "Порядок" при редактировании блока

## 📡 API

Подробная документация API доступна в файле [API.md](./API.md).

### Основные endpoints:

- `GET /api/settings/pages` - Получение списка страниц
- `POST /api/settings/pages` - Создание страницы
- `PUT /api/settings/pages` - Обновление страницы
- `DELETE /api/settings/blocks` - Удаление блока
- `PUT /api/settings/blocks` - Перемещение блока
- `POST /api/settings/save-page` - Сохранение страницы
- `POST /api/settings/save-block` - Сохранение блока

## 🎨 Кастомизация

### Добавление нового типа блока

1. Добавьте конфигурацию в `AdvancedEditorJS.astro`:
```javascript
newBlockType: {
  // Конфигурация инструментов EditorJS
}
```

2. Обновите формы создания и редактирования блоков
3. Добавьте описание инструментов в `toolsInfo`

### Добавление нового плагина EditorJS

1. Установите плагин: `npm install @editorjs/plugin-name`
2. Импортируйте в `AdvancedEditorJS.astro`
3. Добавьте в конфигурации нужных типов блоков

## 🔒 Безопасность

- Все API endpoints защищены аутентификацией
- HTML контент санитизируется при выводе
- Загрузка файлов ограничена по типу и размеру

## 📝 Примеры

Примеры использования и интеграции доступны в файле [Examples.md](./Examples.md).
