# Архитектура системы настроек страниц

## 🏗️ Общая архитектура

Система настроек страниц построена по модульному принципу и состоит из следующих компонентов:

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend (Astro)                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Admin Pages   │  │  Public Pages   │  │  Components  │ │
│  │                 │  │                 │  │              │ │
│  │ • Page Editor   │  │ • Dynamic       │  │ • EditorJS   │ │
│  │ • Block Editor  │  │   Rendering     │  │ • BlockEditor│ │
│  │ • Settings UI   │  │ • SEO Meta      │  │ • PageForm   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                      API Layer                             │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Pages API     │  │   Blocks API    │  │  Media API   │ │
│  │                 │  │                 │  │              │ │
│  │ • CRUD Pages    │  │ • CRUD Blocks   │  │ • Upload     │ │
│  │ • Validation    │  │ • Reordering    │  │ • Management │ │
│  │ • Auth Check    │  │ • Auth Check    │  │ • Auth Check │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Business Logic                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Settings Loader │  │ Settings Saver  │  │ Validators   │ │
│  │                 │  │                 │  │              │ │
│  │ • Load JSON     │  │ • Save JSON     │  │ • Data Valid │ │
│  │ • Parse Data    │  │ • Backup        │  │ • Type Check │ │
│  │ • Error Handle  │  │ • Error Handle  │  │ • Auth Valid │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Data Layer                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   JSON Files    │  │   Media Files   │  │  TypeScript  │ │
│  │                 │  │                 │  │              │ │
│  │ • page.json     │  │ • /uploads/     │  │ • types.ts   │ │
│  │ • Structured    │  │ • Images        │  │ • Interfaces │ │
│  │ • Versioned     │  │ • Documents     │  │ • Validation │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📁 Структура файлов

```
src/
├── settings/                          # Основной модуль настроек
│   ├── components/admin/               # Компоненты админки
│   │   ├── PageEditForm.astro         # Форма редактирования страницы
│   │   └── blocks/
│   │       └── BlockEditor.astro      # Редактор блоков
│   ├── data/
│   │   └── page.json                  # Данные страниц и блоков
│   ├── utils/
│   │   ├── settingsLoader.js          # Утилиты загрузки
│   │   └── settingsSaver.js           # Утилиты сохранения
│   └── types.ts                       # TypeScript типы
├── components/editor/
│   └── AdvancedEditorJS.astro         # Компонент EditorJS
├── pages/
│   ├── api/settings/                  # API endpoints
│   │   ├── pages.js                   # CRUD страниц
│   │   ├── blocks.js                  # CRUD блоков
│   │   ├── save-page.js               # Сохранение страниц
│   │   └── save-block.js              # Сохранение блоков
│   └── admin/settings/pages/          # Админка
│       ├── index.astro                # Список страниц
│       ├── new.astro                  # Создание страницы
│       └── edit/[id]/                 # Редактирование
│           ├── [id].astro             # Редактирование страницы
│           └── block/
│               ├── new.astro          # Создание блока
│               └── edit.astro         # Редактирование блока
└── docs/settings/                     # Документация
    ├── README.md                      # Основная документация
    ├── API.md                         # Документация API
    ├── Examples.md                    # Примеры использования
    └── Architecture.md                # Архитектура (этот файл)
```

## 🔄 Поток данных

### 1. Создание страницы
```
User Input → PageEditForm → save-page API → settingsSaver → page.json
```

### 2. Создание блока
```
User Input → BlockEditor → save-block API → settingsSaver → page.json
```

### 3. Отображение страницы
```
page.json → settingsLoader → PageEditForm → BlockEditor → User Interface
```

### 4. Публичное отображение
```
page.json → settingsLoader → [slug].astro → Rendered Page
```

## 🧩 Компоненты системы

### Frontend Components

#### PageEditForm.astro
- **Назначение**: Форма редактирования страницы
- **Функции**:
  - Редактирование метаданных страницы
  - Управление SEO настройками
  - Интеграция с BlockEditor
- **Входные данные**: ID страницы, данные страницы
- **Выходные данные**: Обновленные данные страницы

#### BlockEditor.astro
- **Назначение**: Управление блоками страницы
- **Функции**:
  - Отображение списка блоков
  - Создание/редактирование/удаление блоков
  - Изменение порядка блоков
- **Входные данные**: Массив блоков, ID страницы
- **Выходные данные**: Обновленный список блоков

#### AdvancedEditorJS.astro
- **Назначение**: Редактор контента на основе EditorJS
- **Функции**:
  - Конфигурация инструментов по типу блока
  - Сохранение данных в JSON формате
  - Поддержка различных плагинов
- **Входные данные**: Тип блока, начальные данные
- **Выходные данные**: JSON данные EditorJS

### API Endpoints

#### /api/settings/pages
- **GET**: Получение списка страниц или конкретной страницы
- **POST**: Создание новой страницы
- **PUT**: Обновление существующей страницы
- **DELETE**: Удаление страницы

#### /api/settings/blocks
- **DELETE**: Удаление блока
- **PUT**: Перемещение блока (изменение порядка)

#### /api/settings/save-page
- **POST**: Сохранение данных страницы через форму

#### /api/settings/save-block
- **POST**: Сохранение данных блока через форму

### Business Logic

#### settingsLoader.js
```javascript
export async function loadPageSettings() {
  // Загрузка и парсинг page.json
  // Обработка ошибок
  // Возврат структурированных данных
}
```

#### settingsSaver.js
```javascript
export async function savePageSettings(settings) {
  // Валидация данных
  // Создание резервной копии
  // Сохранение в page.json
  // Обработка ошибок
}
```

## 🔐 Безопасность

### Аутентификация
- Все API endpoints проверяют аутентификацию
- Использование функции `isAuthenticated()` из `src/utils/auth.js`
- Защита от несанкционированного доступа

### Валидация данных
- Проверка типов данных на уровне TypeScript
- Валидация входных параметров в API
- Санитизация HTML контента

### Файловая безопасность
- Ограничения на типы загружаемых файлов
- Проверка размера файлов
- Безопасное хранение в папке uploads

## 📊 Модель данных

### Основные сущности

#### PageConfig
```typescript
interface PageConfig {
  id: string;                    // Уникальный идентификатор
  template: string;              // Шаблон страницы
  blocks: BlockConfig[];         // Массив блоков
  media: MediaItem[];            // Медиа файлы
  seo: SEOConfig;               // SEO настройки
  visible: boolean;             // Видимость страницы
  url: LocalizedString;         // URL для разных языков
}
```

#### BlockConfig
```typescript
interface BlockConfig {
  id: string;                   // Уникальный идентификатор
  name?: string;                // Название блока
  type: string;                 // Тип блока
  enabled: boolean;             // Включен ли блок
  order?: number;               // Порядок отображения
  content: LocalizedContent;    // Контент для разных языков
}
```

#### SEOConfig
```typescript
interface SEOConfig {
  title: LocalizedString;       // Заголовок страницы
  description: LocalizedString; // Описание страницы
  keywords?: LocalizedString;   // Ключевые слова
}
```

## 🔧 Расширяемость

### Добавление нового типа блока

1. **Обновить конфигурацию EditorJS**:
```javascript
// В AdvancedEditorJS.astro
const toolConfigs = {
  // ... существующие типы
  newBlockType: {
    // Конфигурация инструментов
  }
};
```

2. **Добавить в формы**:
```html
<option value="newBlockType">Новый тип блока</option>
```

3. **Обновить описания инструментов**:
```javascript
const toolsInfo = {
  // ... существующие типы
  newBlockType: ['Список', 'доступных', 'инструментов']
};
```

### Добавление нового плагина EditorJS

1. **Установка**: `npm install @editorjs/new-plugin`
2. **Импорт**: `import NewPlugin from '@editorjs/new-plugin';`
3. **Конфигурация**: Добавить в нужные типы блоков
4. **Тестирование**: Проверить работу в разных типах блоков

### Добавление нового языка

1. **Обновить типы**:
```typescript
type LocalizedString = { 
  ru: string; 
  en: string; 
  de: string; // новый язык
};
```

2. **Обновить формы**: Добавить поля для нового языка
3. **Обновить API**: Поддержка нового языка в endpoints

## 🚀 Производительность

### Оптимизации

1. **Ленивая загрузка**: EditorJS загружается только при необходимости
2. **Кэширование**: Данные страниц кэшируются в памяти
3. **Минификация**: JSON данные сжимаются при сохранении
4. **Индексация**: Быстрый поиск страниц по ID

### Мониторинг

1. **Размер данных**: Отслеживание размера page.json
2. **Время загрузки**: Мониторинг скорости загрузки страниц
3. **Ошибки**: Логирование ошибок API и компонентов

## 🔄 Миграции и обновления

### Версионирование данных
- Поле `version` в структуре данных
- Автоматические миграции при обновлении
- Резервные копии перед миграцией

### Обратная совместимость
- Поддержка старых форматов данных
- Постепенная миграция блоков
- Fallback для отсутствующих полей

Эта архитектура обеспечивает масштабируемость, безопасность и простоту использования системы настроек страниц.
