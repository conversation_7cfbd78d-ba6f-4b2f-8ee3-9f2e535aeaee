# Миграция файлов системы настроек

## Описание

Была проведена реорганизация файлов системы настроек для улучшения структуры проекта и упрощения интеграции в другие проекты. Все файлы, относящиеся к системе настроек страниц, теперь находятся в папке `src/settings/`.

## Перемещенные файлы

### 1. Утилиты

**Перемещено:**
- `src/utils/editorjs-parser.js` → `src/settings/utils/editorjs-parser.js`

**Обновлены импорты в файлах:**
- `src/settings/components/content/BlockRenderer.astro`
- `src/pages/[slug].astro`
- `examples/content-parser-example.js`
- `tests/editorjs-parser.test.js`
- `debug-parser.js` (удален)
- `docs/content-rendering.md`

### 2. Компоненты контента

**Перемещено:**
- `src/components/content/` → `src/settings/components/content/`
  - `ContentRenderer.astro`
  - `BlockRenderer.astro`

**Обновлены импорты в файлах:**
- `src/pages/[slug].astro`
- `docs/content-rendering.md`

### 3. Компоненты редактора

**Перемещено:**
- `src/components/editor/` → `src/settings/components/editor/`
  - `AdvancedEditorJS.astro`
  - `EditorJS.astro`

**Обновлены импорты в файлах:**
- `src/pages/admin/settings/pages/edit/[id]/block/new.astro`
- `src/pages/admin/settings/pages/edit/[id]/block/edit.astro`
- `docs/settings/README.md`
- `docs/settings/Architecture.md`

## Итоговая структура

```
src/settings/
├── components/
│   ├── admin/                     # Компоненты админ-панели
│   │   ├── PageEditForm.astro
│   │   ├── PagesList.astro
│   │   └── blocks/
│   │       └── BlockEditor.astro
│   ├── content/                   # Компоненты рендеринга контента
│   │   ├── ContentRenderer.astro  # Рендеринг массива блоков
│   │   └── BlockRenderer.astro    # Рендеринг отдельного блока
│   └── editor/                    # Компоненты редактора
│       ├── AdvancedEditorJS.astro # Продвинутый редактор
│       └── EditorJS.astro         # Базовый редактор
├── data/
│   └── page.json                  # Данные страниц
├── utils/
│   ├── editorjs-parser.js         # Парсер Editor.js → HTML
│   ├── settingsLoader.js          # Загрузка настроек
│   ├── settingsSaver.js           # Сохранение настроек
│   └── cleanupPages.js            # Утилиты очистки
├── types.ts                       # TypeScript типы
└── README.md                      # Документация
```

## Файлы, которые НЕ были перемещены

Следующие файлы остались в `src/utils/`, так как они используются не только системой настроек:

- `auth.js` - используется для общей аутентификации в админ-панели
- `dataValidation.js` - используется для валидации товаров
- Другие утилиты общего назначения

## Преимущества реорганизации

### 1. Модульность
- Все компоненты системы настроек находятся в одном месте
- Легко понять, какие файлы относятся к системе настроек
- Упрощена навигация по проекту

### 2. Переносимость
- Систему настроек можно легко интегрировать в другие проекты
- Достаточно скопировать папку `src/settings/` и обновить пути импорта
- Минимальные зависимости от остального кода

### 3. Поддержка
- Упрощено обслуживание и развитие системы настроек
- Четкое разделение ответственности между модулями
- Легче добавлять новые функции

## Проверка работоспособности

После миграции были проверены:

✅ **Фронтенд страницы:**
- `/contacts` - отображение контента из Editor.js
- `/test-page` - отображение сложного контента с изображениями

✅ **Админ-панель:**
- `/admin/settings/pages` - список страниц
- `/admin/settings/pages/edit/[id]` - редактирование страниц
- `/admin/settings/pages/edit/[id]/block/edit` - редактирование блоков

✅ **API endpoints:**
- Сохранение страниц
- Сохранение блоков
- Загрузка изображений

✅ **Компоненты:**
- `ContentRenderer` - рендеринг блоков на страницах
- `BlockRenderer` - рендеринг отдельных блоков
- `AdvancedEditorJS` - редактор контента
- `EditorJSParser` - парсинг JSON в HTML

## Обратная совместимость

Все существующие функции сохранены:
- API endpoints работают без изменений
- Структура данных в `page.json` не изменилась
- Пользовательский интерфейс остался прежним
- Все ссылки и маршруты работают корректно

## Следующие шаги

1. **Тестирование** - провести полное тестирование всех функций
2. **Документация** - обновить документацию для разработчиков
3. **Оптимизация** - возможные улучшения производительности
4. **Расширение** - добавление новых типов блоков и функций

## Заключение

Реорганизация успешно завершена. Система настроек теперь имеет четкую структуру и готова для интеграции в другие проекты. Все функции работают корректно, обратная совместимость сохранена.
