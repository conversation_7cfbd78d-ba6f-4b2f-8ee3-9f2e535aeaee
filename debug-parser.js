import editorJSParser from './src/settings/utils/editorjs-parser.js';

const testData = {
  "blocks": [
    {
      "type": "header",
      "data": {
        "text": "Тестовый заголовок",
        "level": 2
      }
    }
  ]
};

console.log('Входные данные:', JSON.stringify(testData, null, 2));

const result = editorJSParser.parseToHTML(JSON.stringify(testData));
console.log('Результат парсинга:', result);
console.log('Длина результата:', result.length);

// Проверим парсер напрямую
import edjsHTML from 'editorjs-html';
const parser = edjsHTML();
const directResult = parser.parse(testData);
console.log('Прямой результат библиотеки:', directResult);
