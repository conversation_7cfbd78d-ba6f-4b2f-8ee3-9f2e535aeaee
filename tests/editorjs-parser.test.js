// Простые тесты для парсера Editor.js
// Запуск: node tests/editorjs-parser.test.js

import editorJSParser from '../src/settings/utils/editorjs-parser.js';

// Функция для запуска тестов
function runTests() {
  console.log('🧪 Запуск тестов парсера Editor.js...\n');
  
  let passed = 0;
  let failed = 0;
  
  function test(name, testFn) {
    try {
      testFn();
      console.log(`✅ ${name}`);
      passed++;
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
      failed++;
    }
  }
  
  function assert(condition, message) {
    if (!condition) {
      throw new Error(message);
    }
  }
  
  // Тест 1: Парсинг простого заголовка
  test('Парсинг простого заголовка', () => {
    const data = {
      "blocks": [
        {
          "type": "header",
          "data": {
            "text": "Тестовый заголовок",
            "level": 2
          }
        }
      ]
    };
    
    const result = editorJSParser.parseToHTML(JSON.stringify(data));
    assert(result.includes('Тестовый заголовок'), 'Заголовок должен быть в результате');
    assert(result.includes('<h2'), 'Должен быть тег h2');
  });
  
  // Тест 2: Парсинг параграфа
  test('Парсинг параграфа', () => {
    const data = {
      "blocks": [
        {
          "type": "paragraph",
          "data": {
            "text": "Тестовый параграф"
          }
        }
      ]
    };
    
    const result = editorJSParser.parseToHTML(JSON.stringify(data));
    assert(result.includes('Тестовый параграф'), 'Параграф должен быть в результате');
    assert(result.includes('<p'), 'Должен быть тег p');
  });
  
  // Тест 3: Парсинг списка
  test('Парсинг списка', () => {
    const data = {
      "blocks": [
        {
          "type": "list",
          "data": {
            "style": "unordered",
            "items": [
              { "content": "Пункт 1", "meta": {}, "items": [] },
              { "content": "Пункт 2", "meta": {}, "items": [] }
            ]
          }
        }
      ]
    };
    
    const result = editorJSParser.parseToHTML(JSON.stringify(data));
    assert(result.includes('Пункт 1'), 'Первый пункт должен быть в результате');
    assert(result.includes('Пункт 2'), 'Второй пункт должен быть в результате');
    assert(result.includes('<ul'), 'Должен быть тег ul');
  });
  
  // Тест 4: Обработка пустых данных
  test('Обработка пустых данных', () => {
    const result = editorJSParser.parseToHTML('');
    assert(result === '', 'Пустые данные должны возвращать пустую строку');
  });
  
  // Тест 5: Обработка некорректного JSON
  test('Обработка некорректного JSON', () => {
    const result = editorJSParser.parseToHTML('invalid json');
    assert(result === 'invalid json', 'Некорректный JSON должен возвращаться как есть');
  });
  
  // Тест 6: Парсинг блока контента
  test('Парсинг блока контента', () => {
    const block = {
      content: {
        ru: JSON.stringify({
          "blocks": [
            {
              "type": "paragraph",
              "data": {
                "text": "Русский текст"
              }
            }
          ]
        }),
        en: JSON.stringify({
          "blocks": [
            {
              "type": "paragraph",
              "data": {
                "text": "English text"
              }
            }
          ]
        })
      }
    };
    
    const ruResult = editorJSParser.parseBlockContent(block, 'ru');
    const enResult = editorJSParser.parseBlockContent(block, 'en');
    
    assert(ruResult.includes('Русский текст'), 'Должен быть русский текст');
    assert(enResult.includes('English text'), 'Должен быть английский текст');
  });
  
  // Тест 7: Обработка блока без контента
  test('Обработка блока без контента', () => {
    const block = {};
    const result = editorJSParser.parseBlockContent(block, 'ru');
    assert(result === '', 'Блок без контента должен возвращать пустую строку');
  });
  
  // Тест 8: Парсинг изображения с полными данными
  test('Парсинг изображения с полными данными', () => {
    const data = {
      "blocks": [
        {
          "type": "image",
          "data": {
            "caption": "Тестовое изображение",
            "file": {
              "url": "/test-image.jpg"
            }
          }
        }
      ]
    };
    
    const result = editorJSParser.parseToHTML(JSON.stringify(data));
    assert(result.includes('/test-image.jpg'), 'URL изображения должен быть в результате');
    assert(result.includes('Тестовое изображение'), 'Подпись должна быть в результате');
  });
  
  // Тест 9: Парсинг изображения без данных
  test('Парсинг изображения без данных', () => {
    const data = {
      "blocks": [
        {
          "type": "image",
          "data": {}
        }
      ]
    };
    
    const result = editorJSParser.parseToHTML(JSON.stringify(data));
    assert(result.includes('Изображение не найдено'), 'Должно быть сообщение об ошибке');
  });
  
  // Тест 10: Парсинг кнопки
  test('Парсинг кнопки', () => {
    const data = {
      "blocks": [
        {
          "type": "button",
          "data": {
            "text": "Нажми меня",
            "link": "https://example.com"
          }
        }
      ]
    };
    
    const result = editorJSParser.parseToHTML(JSON.stringify(data));
    assert(result.includes('Нажми меня'), 'Текст кнопки должен быть в результате');
    assert(result.includes('https://example.com'), 'Ссылка должна быть в результате');
  });
  
  // Результаты тестов
  console.log(`\n📊 Результаты тестов:`);
  console.log(`✅ Пройдено: ${passed}`);
  console.log(`❌ Провалено: ${failed}`);
  console.log(`📈 Общий результат: ${passed}/${passed + failed} (${Math.round(passed / (passed + failed) * 100)}%)`);
  
  if (failed === 0) {
    console.log('\n🎉 Все тесты пройдены успешно!');
  } else {
    console.log('\n⚠️  Некоторые тесты провалились. Проверьте код парсера.');
  }
}

// Запуск тестов
runTests();
