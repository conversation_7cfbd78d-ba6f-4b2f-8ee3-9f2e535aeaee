// EditorJS инициализация для админ-панели
class EditorJSManager {
  constructor() {
    this.editors = new Map();
    this.isLoaded = false;
    this.loadPromise = null;
    this.scriptsLoaded = {
      editorjs: false,
      header: false,
      list: false,
      paragraph: false,
      quote: false,
      code: false,
      delimiter: false,
      table: false,
      link: false
    };
  }

  // Загрузка EditorJS и плагинов
  async loadEditorJS() {
    if (this.isLoaded) return;
    if (this.loadPromise) return this.loadPromise;

    this.loadPromise = new Promise(async (resolve, reject) => {
      try {
        console.log('🔄 Загружаем EditorJS и плагины...');

        // Загружаем EditorJS и основные плагины через CDN
        await this.loadScript('https://cdn.jsdelivr.net/npm/@editorjs/editorjs@latest', 'editorjs');
        await this.loadScript('https://cdn.jsdelivr.net/npm/@editorjs/header@latest', 'header');
        await this.loadScript('https://cdn.jsdelivr.net/npm/@editorjs/list@latest', 'list');

        // Ждем, пока все скрипты загрузятся
        await this.waitForGlobals();

        this.isLoaded = true;
        console.log('✅ EditorJS и плагины загружены успешно');
        resolve();
      } catch (error) {
        console.error('❌ Ошибка при загрузке EditorJS:', error);
        reject(error);
      }
    });

    return this.loadPromise;
  }

  // Ожидание загрузки глобальных переменных
  async waitForGlobals() {
    return new Promise((resolve) => {
      const checkGlobals = () => {
        if (window.EditorJS && window.Header && window.List) {
          console.log('✅ Все необходимые библиотеки загружены');
          resolve();
        } else {
          console.log('⏳ Ожидаем загрузки библиотек...', {
            EditorJS: !!window.EditorJS,
            Header: !!window.Header,
            List: !!window.List
          });
          setTimeout(checkGlobals, 100);
        }
      };
      checkGlobals();
    });
  }

  // Загрузка скрипта
  loadScript(src, name) {
    return new Promise((resolve, reject) => {
      // Проверяем, не загружен ли уже скрипт
      if (document.querySelector(`script[src="${src}"]`) || this.scriptsLoaded[name]) {
        this.scriptsLoaded[name] = true;
        resolve();
        return;
      }

      console.log(`📦 Загружаем ${name}: ${src}`);
      const script = document.createElement('script');
      script.src = src;
      script.onload = () => {
        console.log(`✅ ${name} загружен`);
        this.scriptsLoaded[name] = true;
        resolve();
      };
      script.onerror = (error) => {
        console.error(`❌ Ошибка загрузки ${name}:`, error);
        reject(error);
      };
      document.head.appendChild(script);
    });
  }

  // Создание экземпляра редактора
  async createEditor(config) {
    try {
      console.log('🚀 Создаем редактор с конфигурацией:', config);
      await this.loadEditorJS();

      const {
        id,
        placeholder = 'Начните писать...',
        data = null,
        readonly = false,
        minHeight = 300,
        onChange = null
      } = config;

      const editorId = `editor-${id}`;
      const hiddenInputId = `${id}-data`;

      // Проверяем, что элемент существует
      const editorElement = document.getElementById(editorId);
      const hiddenInput = document.getElementById(hiddenInputId);

      if (!editorElement) {
        console.error(`❌ EditorJS: Элемент с ID ${editorId} не найден`);
        return null;
      }

      console.log('📝 Настраиваем инструменты редактора...');

      // Конфигурация инструментов (упрощенная версия)
      const tools = {
        header: {
          class: window.Header,
          config: {
            placeholder: 'Введите заголовок...',
            levels: [1, 2, 3, 4, 5, 6],
            defaultLevel: 2
          }
        },
        list: {
          class: window.List,
          inlineToolbar: true,
          config: {
            defaultStyle: 'unordered'
          }
        }
      };

      console.log('🔧 Создаем экземпляр EditorJS...');

      // Создание экземпляра редактора
      const editor = new window.EditorJS({
        holder: editorId,
        tools: tools,
        data: data || {},
        readOnly: readonly,
        placeholder: placeholder,
        minHeight: minHeight,
        onChange: async (api, event) => {
          try {
            const outputData = await editor.save();

            // Сохраняем данные в скрытое поле
            if (hiddenInput) {
              hiddenInput.value = JSON.stringify(outputData);
            }

            // Вызываем пользовательский обработчик, если он задан
            if (onChange && typeof window[onChange] === 'function') {
              window[onChange](outputData, api, event);
            }

            // Сохраняем данные в глобальную переменную для доступа из форм
            window[`${editorId}_data`] = outputData;
            window.editorjsLastData = JSON.stringify(outputData);

          } catch (error) {
            console.error('❌ EditorJS: Ошибка при сохранении данных:', error);
          }
        },
        onReady: () => {
          console.log(`✅ EditorJS: Редактор ${editorId} готов к работе`);

          // Устанавливаем минимальную высоту
          const editorContainer = document.querySelector(`#${editorId} .codex-editor`);
          if (editorContainer) {
            editorContainer.style.minHeight = `${minHeight}px`;
          }
        }
      });

      // Сохраняем экземпляр редактора
      this.editors.set(editorId, editor);
      window[`${editorId}_instance`] = editor;

      // Функции для работы с данными
      window[`get${editorId}Data`] = async () => {
        try {
          return await editor.save();
        } catch (error) {
          console.error('❌ EditorJS: Ошибка при получении данных:', error);
          return null;
        }
      };

      window[`set${editorId}Data`] = async (newData) => {
        if (newData) {
          try {
            await editor.render(newData);
          } catch (error) {
            console.error('❌ EditorJS: Ошибка при установке данных:', error);
          }
        }
      };

      console.log(`🎉 Редактор ${editorId} создан успешно`);
      return editor;

    } catch (error) {
      console.error('❌ Критическая ошибка при создании редактора:', error);
      return null;
    }
  }

  // Получение экземпляра редактора
  getEditor(id) {
    const editorId = `editor-${id}`;
    return this.editors.get(editorId);
  }

  // Уничтожение редактора
  destroyEditor(id) {
    const editorId = `editor-${id}`;
    const editor = this.editors.get(editorId);
    if (editor && editor.destroy) {
      editor.destroy();
      this.editors.delete(editorId);
    }
  }
}

// Создаем глобальный экземпляр менеджера
window.EditorJSManager = new EditorJSManager();

// Функция для быстрой инициализации редактора
window.initEditorJS = async function(config) {
  return await window.EditorJSManager.createEditor(config);
};

// Функция для инициализации редактора с ожиданием загрузки DOM
window.initEditorJSWhenReady = function(config) {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      window.initEditorJS(config);
    });
  } else {
    window.initEditorJS(config);
  }
};
